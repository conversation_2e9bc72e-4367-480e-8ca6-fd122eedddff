import { useQuery } from '@tanstack/react-query'
import type { ColumnDef } from '@tanstack/react-table'
import { flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table'
import { useAtom } from 'jotai'
import { ArrowDownToLineIcon, ChevronLeftIcon, ChevronRightIcon } from 'lucide-react'
import { useMemo, useState } from 'react'
import { toast } from 'sonner'

import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { api } from '@/helpers/lib/api-client'

import type { DownloadStocksListParams, DownLoadStocksListResult } from '../atoms'
import { exportHoldings, fundIdAtom, markAtom, selectedYear<PERSON>tom } from '../atoms'
import { useDownload } from '../hooks/useDownload'

type StockData = {
  stockName: string
  stockCode: string
  rptDate: string
  proportiontototalstockinvestments: number
  windIndustry: string | null
  gpType: string | null
  FFMark: string | null
  GTMark: string | null
}

export type StockDialogProps = {
  isOpen: boolean
  setIsOpen: (value: boolean) => void
  title: string
}

export function StockDialog({ isOpen, setIsOpen, title }: StockDialogProps) {
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })
  const [windId] = useAtom(fundIdAtom)
  const [year] = useAtom(selectedYearAtom)
  const [mark] = useAtom(markAtom)

  const { data } = useQuery({
    queryKey: ['stocks', windId, year, mark, pagination],
    queryFn: async () => {
      const res = await api.post('/api/funds/get-stocks-list', {
        windId: windId!,
        year,
        mark,
        paging: {
          page: pagination.pageIndex,
          size: pagination.pageSize,
        },
      })
      if (res.status === 'success') {
        return res.data
      }
      else {
        toast.error(res.data)
      }
    },
  })

  // ${已选择的基金名称}${已选择的报告时间}${已选择的公司分类}公司列表
  const { isPending, download } = useDownload<DownloadStocksListParams, DownLoadStocksListResult>(exportHoldings, title)

  const columns: Array<ColumnDef<StockData>> = [
    {
      accessorKey: 'stockName',
      header: '股票名称',
    },
    {
      accessorKey: 'stockCode',
      header: '股票代码',
    },
    {
      accessorKey: 'rptDate',
      header: '报告时间',
    },
    {
      accessorKey: 'proportiontototalstockinvestments',
      header: '投资占比',
      cell: ({ row }) => `${(row.getValue('proportiontototalstockinvestments') as number).toFixed(2)}%`,
    },
    {
      accessorKey: 'windIndustry',
      header: 'WIND 行业名称',
    },
    {
      accessorKey: 'gpType',
      header: '本数据库行业分类',
    },
    {
      accessorKey: 'FFMark',
      header: '是否所属化石燃料相关行业',
    },
    {
      accessorKey: 'GTMark',
      header: '是否所属高碳类型行业',
    },
  ]

  const defaultData = useMemo(() => [], [])

  const table = useReactTable({
    data: (data?.list || defaultData),
    columns,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    rowCount: data?.pagination?.total,
    state: {
      pagination,
    },
    onPaginationChange: setPagination,
  })

  const handleDownload = () => {
    download({
      windId: windId!,
      year: year!,
      mark: mark!,
    })
  }

  const handleNextPage = () => {
    table.nextPage()
  }

  // 生成智能分页按钮数组
  const generatePaginationItems = () => {
    const currentPage = table.getState().pagination.pageIndex
    const totalPages = table.getPageCount()

    if (totalPages <= 5) {
      // 总页数不超过5页，显示所有页码
      return Array.from({ length: totalPages }, (_, i) => ({
        type: 'page' as const,
        page: i,
        label: (i + 1).toString(),
        key: `page-${i}`,
      }))
    }

    const items: Array<{ type: 'page' | 'ellipsis', page?: number, label: string, key: string }> = []

    if (currentPage <= 2) {
      // 当前页在前3页：显示 1, 2, 3, 4, ..., 最后页
      for (let i = 0; i < 4; i++) {
        items.push({ type: 'page', page: i, label: (i + 1).toString(), key: `page-${i}` })
      }
      items.push(
        { type: 'ellipsis', label: '...', key: 'ellipsis-end' },
        { type: 'page', page: totalPages - 1, label: totalPages.toString(), key: `page-${totalPages - 1}` },
      )
    }
    else if (currentPage >= totalPages - 3) {
      // 当前页在后3页：显示 1, ..., 倒数第4页, 倒数第3页, 倒数第2页, 最后页
      items.push(
        { type: 'page', page: 0, label: '1', key: 'page-0' },
        { type: 'ellipsis', label: '...', key: 'ellipsis-start' },
      )
      for (let i = totalPages - 4; i < totalPages; i++) {
        items.push({ type: 'page', page: i, label: (i + 1).toString(), key: `page-${i}` })
      }
    }
    else {
      // 当前页在中间：显示 1, ..., 当前页-1, 当前页, 当前页+1, ..., 最后页
      items.push(
        { type: 'page', page: 0, label: '1', key: 'page-0' },
        { type: 'ellipsis', label: '...', key: 'ellipsis-start' },
        { type: 'page', page: currentPage - 1, label: currentPage.toString(), key: `page-${currentPage - 1}` },
        { type: 'page', page: currentPage, label: (currentPage + 1).toString(), key: `page-${currentPage}` },
        { type: 'page', page: currentPage + 1, label: (currentPage + 2).toString(), key: `page-${currentPage + 1}` },
        { type: 'ellipsis', label: '...', key: 'ellipsis-end' },
        { type: 'page', page: totalPages - 1, label: totalPages.toString(), key: `page-${totalPages - 1}` },
      )
    }

    return items
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-screen h-screen rounded-none !flex !flex-col gap-3.5" showCloseButton={false}>
        <div className="flex justify-between items-center">
          <Button variant="ghost" asChild onClick={() => setIsOpen(false)}>
            <div className="flex items-center gap-2">
              <ChevronLeftIcon className="!size-5" />
              <span className="text-xl">返回</span>
            </div>
          </Button>
          <DialogTitle className="text-[28px] font-bold">{title}</DialogTitle>
          <Button
            size="lg"
            onClick={handleDownload}
            disabled={isPending}
            className="bg-[#001e3d] text-white font-normal tracking-normal hover:bg-[#002a4d] disabled:opacity-50 rounded-none"
          >
            <ArrowDownToLineIcon className="!w-5 !h-5" />
            <span className="text-base">{isPending ? '下载中...' : '下载全部'}</span>
          </Button>
        </div>
        <div className="flex flex-col gap-4 py-4 h-full">
          <div className="">
            <div className="overflow-auto">
              <div className="min-w-[1042px]">
                <Table className="w-full table-fixed border-collapse">
                  <TableHeader className="bg-[#001e3d]">
                    {table.getHeaderGroups().map(headerGroup => (
                      <TableRow key={headerGroup.id}>
                        {headerGroup.headers.map(header => (
                          <TableHead key={header.id} className="bg-[#001e3d] text-white border-b border-r border-white text-center !text-wrap p-5 text-lg last:border-r-0">
                            {header.isPlaceholder
                              ? null
                              : flexRender(
                                  header.column.columnDef.header,
                                  header.getContext(),
                                )}
                          </TableHead>
                        ))}
                      </TableRow>
                    ))}
                  </TableHeader>
                </Table>
              </div>
              <div className="overflow-auto min-w-[1042px] max-h-[900px]">
                <Table className="w-full table-fixed border-collapse">
                  <TableBody>
                    {table.getRowModel().rows?.length ? (
                      table.getRowModel().rows.map((row, index) => (
                        <TableRow
                          key={row.id}
                          data-state={row.getIsSelected() && 'selected'}
                          className={`${index % 2 === 0 ? 'bg-[rgba(240,240,240,1)]' : 'bg-[rgba(245,245,245,1)]'
                          }`}
                        >
                          {row.getVisibleCells().map(cell => (
                            <TableCell key={cell.id} className="text-center p-5 text-wrap whitespace-normal text-xl border-r border-white last:border-r-0">
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext(),
                              )}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell
                          colSpan={columns.length}
                          className="h-24 text-center"
                        >
                          暂无数据
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </div>
            <div className="flex items-center justify-end space-x-2 mt-10">
              <Button
                variant="outline"
                className="size-8"
                size="icon"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                <ChevronLeftIcon />
              </Button>
              {generatePaginationItems().map((item) => {
                if (item.type === 'ellipsis') {
                  return (
                    <span
                      key={item.key}
                      className="flex items-center justify-center size-8 text-sm"
                    >
                      {item.label}
                    </span>
                  )
                }

                return (
                  <Button
                    key={item.key}
                    variant="outline"
                    className={`size-8 ${item.page === table.getState().pagination.pageIndex
                      ? 'bg-[#001e3d] text-white border-[#001e3d] hover:bg-[#001e3d] hover:text-white'
                      : ''
                      }`}
                    size="icon"
                    onClick={() => table.setPageIndex(item.page!)}
                  >
                    {item.label}
                  </Button>
                )
              })}
              <Button
                variant="outline"
                className="size-8"
                size="icon"
                onClick={handleNextPage}
                disabled={!table.getCanNextPage()}
              >
                <ChevronRightIcon />
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
