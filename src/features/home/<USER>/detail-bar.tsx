import ReactECharts from 'echarts-for-react'
import { useAtom, useAtomValue } from 'jotai'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'

import { Calendar7Icon } from '@/components/icons/calendar-7-icon'
import { SelectWithIcon } from '@/components/select-with-icon'

import { currentStockAtom, getCompanyTypeStatisticsQueryAtom, getYearsQueryAtom, markAtom, selectedYearAtom } from '../atoms'
import { useBarOption } from '../hooks/useBarOption'
import { StockDialog } from './stock-dialog'

interface ChartClickParams {
  data: {
    mark: 'ff' | 'gt' | 'nt_ff' | 'nt_gt'
  }
}

export function DetailBar() {
  const { data } = useAtomValue(getCompanyTypeStatisticsQueryAtom)
  const { data: yearList } = useAtomValue(getYearsQueryAtom)
  const [year, setYear] = useAtom(selectedYear<PERSON>tom)
  const [currentStock] = useAtom(currentStockAtom)

  const [, setMark] = useAtom(markAtom)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [title, setTitle] = useState<string>()

  const currentStockRef = useRef(currentStock)
  const yearRef = useRef(year)

  currentStockRef.current = currentStock
  yearRef.current = year

  useEffect(() => {
    if (yearList?.length) {
      setYear(yearList[0])
    }
  }, [yearList, setYear])

  const { ffOption, gtOption } = useBarOption({ data })

  const yearOptions = useMemo(() => {
    return yearList?.map(yearItem => ({ label: yearItem, value: yearItem })) || []
  }, [yearList])

  const handleYearChange = useCallback((value: string) => {
    setYear(value)
  }, [setYear])

  const handleFFChartClick = useCallback((params: ChartClickParams) => {
    const { mark } = params.data
    setMark(mark)
    const text = mark === 'ff' ? '化石燃料相关' : '非化石燃料相关'
    setTitle(`${currentStockRef.current}${yearRef.current}${text}公司列表`)
    setIsDialogOpen(true)
  }, [setMark])

  const handleGTChartClick = useCallback((params: ChartClickParams) => {
    const { mark } = params.data
    setMark(mark)
    const text = mark === 'gt' ? '高碳' : '非高碳'
    setTitle(`${currentStockRef.current}${yearRef.current}${text}公司列表`)
    setIsDialogOpen(true)
  }, [setMark])

  const chartStyle = useMemo(() => ({ height: 500 }), [])

  return (
    <div className="flex flex-col gap-7 mt-14 container">
      <div className="flex justify-end">
        <SelectWithIcon
          value={year}
          onValueChange={handleYearChange}
          data={yearOptions}
          icon={<Calendar7Icon className="size-5 mr-3 flex-shrink-0" />}
        />
      </div>
      <div className="pt-4 bg-white rounded shadow">
        {year && (
          <div className="flex flex-row gap-4">
            <div className="w-1/2">
              <div className="relative w-full h-12 items-center flex justify-center">
                <span className="text-[28px] z-10">化石燃料相关行业投资公司数量对比</span>
                <div className="absolute w-[463px] h-[17.8px] opacity-90 bg-gradient-to-r from-[#081d3b] to-white -bottom-0.5 left-22" />
              </div>
              <ReactECharts
                option={ffOption}
                style={chartStyle}
                onEvents={{
                  click: handleFFChartClick,
                }}
              />
            </div>
            <div className="w-1/2">
              <div className="relative w-full h-12 items-center flex justify-center">
                <span className="text-[28px] z-10">高碳类型行业公司数量对比</span>
                <div className="absolute w-[463px] h-[17.8px] opacity-90 bg-gradient-to-r from-[#999] to-white -bottom-0.5 left-34" />
              </div>
              <ReactECharts
                option={gtOption}
                style={chartStyle}
                onEvents={{
                  click: handleGTChartClick,
                }}
              />
            </div>
          </div>
        )}
      </div>
      {isDialogOpen && title && (
        <StockDialog
          isOpen={isDialogOpen}
          setIsOpen={setIsDialogOpen}
          title={title}
        />
      )}
    </div>
  )
}
