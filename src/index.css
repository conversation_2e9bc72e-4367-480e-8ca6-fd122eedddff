@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --font-sans: 'Source Han Sans SC';
  --font-alibaba: 'Alibaba';
  --color-blue: rgba(8, 29, 59, 1);
  --color-green: rgba(102, 204, 0, 1);
  --color-text-gray: rgba(255, 255, 255, 1);

  /* 响应式缩放变量 - 默认为100%（1920px及以上） */
  --scale-factor: 1;
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

/* 响应式缩放媒体查询 */
/* 低于1366px: 70% */
@media (max-width: 1365px) {
  :root {
    --scale-factor: 0.7;
  }
}

/* 1366px-1440px: 80% */
@media (min-width: 1366px) and (max-width: 1439px) {
  :root {
    --scale-factor: 0.8;
  }
}

/* 1440px-1920px: 90% */
@media (min-width: 1440px) and (max-width: 1919px) {
  :root {
    --scale-factor: 0.9;
  }
}

/* 1920px及以上: 100% (默认值，已在:root中设置) */

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* 应用缩放到主要内容区域 */
  #root {
    transform: scale(var(--scale-factor));
    transform-origin: top left;
    width: calc(100vw / var(--scale-factor));
    min-height: calc(100vh / var(--scale-factor));
    overflow-x: auto;
  }

  /* 确保html和body不会出现滚动条问题 */
  html, body {
    overflow-x: hidden;
  }
  .container {
    @apply !max-w-[1300px] mx-auto;
  }

  /* 全局缩放应用 */
  html {
    /* 使用font-size基准缩放作为备用 */
    font-size: calc(16px * var(--scale-factor));
  }
}

@font-face {
  font-family: 'Source Han Sans SC';
  src: url('/src/assets/fonts/SourceHanSansSC.otf.woff2') format('woff2');
}

/* 响应式缩放工具类 */
@layer utilities {
  /* 为所有需要缩放的元素提供基础缩放 */
  .responsive-scale {
    transform: scale(var(--scale-factor));
    transform-origin: top left;
  }

  /* 字体大小响应式缩放 */
  .text-responsive-xs { font-size: calc(0.75rem * var(--scale-factor)); }
  .text-responsive-sm { font-size: calc(0.875rem * var(--scale-factor)); }
  .text-responsive-base { font-size: calc(1rem * var(--scale-factor)); }
  .text-responsive-lg { font-size: calc(1.125rem * var(--scale-factor)); }
  .text-responsive-xl { font-size: calc(1.25rem * var(--scale-factor)); }
  .text-responsive-2xl { font-size: calc(1.5rem * var(--scale-factor)); }
  .text-responsive-3xl { font-size: calc(1.875rem * var(--scale-factor)); }
  .text-responsive-4xl { font-size: calc(2.25rem * var(--scale-factor)); }
  .text-responsive-5xl { font-size: calc(3rem * var(--scale-factor)); }
  .text-responsive-6xl { font-size: calc(3.75rem * var(--scale-factor)); }
  .text-responsive-7xl { font-size: calc(4.5rem * var(--scale-factor)); }
  .text-responsive-8xl { font-size: calc(6rem * var(--scale-factor)); }
  .text-responsive-9xl { font-size: calc(8rem * var(--scale-factor)); }

  /* 间距响应式缩放 */
  .p-responsive-1 { padding: calc(0.25rem * var(--scale-factor)); }
  .p-responsive-2 { padding: calc(0.5rem * var(--scale-factor)); }
  .p-responsive-3 { padding: calc(0.75rem * var(--scale-factor)); }
  .p-responsive-4 { padding: calc(1rem * var(--scale-factor)); }
  .p-responsive-5 { padding: calc(1.25rem * var(--scale-factor)); }
  .p-responsive-6 { padding: calc(1.5rem * var(--scale-factor)); }
  .p-responsive-8 { padding: calc(2rem * var(--scale-factor)); }
  .p-responsive-10 { padding: calc(2.5rem * var(--scale-factor)); }
  .p-responsive-12 { padding: calc(3rem * var(--scale-factor)); }
  .p-responsive-16 { padding: calc(4rem * var(--scale-factor)); }

  .m-responsive-1 { margin: calc(0.25rem * var(--scale-factor)); }
  .m-responsive-2 { margin: calc(0.5rem * var(--scale-factor)); }
  .m-responsive-3 { margin: calc(0.75rem * var(--scale-factor)); }
  .m-responsive-4 { margin: calc(1rem * var(--scale-factor)); }
  .m-responsive-5 { margin: calc(1.25rem * var(--scale-factor)); }
  .m-responsive-6 { margin: calc(1.5rem * var(--scale-factor)); }
  .m-responsive-8 { margin: calc(2rem * var(--scale-factor)); }
  .m-responsive-10 { margin: calc(2.5rem * var(--scale-factor)); }
  .m-responsive-12 { margin: calc(3rem * var(--scale-factor)); }
  .m-responsive-16 { margin: calc(4rem * var(--scale-factor)); }

  /* 宽高响应式缩放 */
  .w-responsive-4 { width: calc(1rem * var(--scale-factor)); }
  .w-responsive-6 { width: calc(1.5rem * var(--scale-factor)); }
  .w-responsive-8 { width: calc(2rem * var(--scale-factor)); }
  .w-responsive-10 { width: calc(2.5rem * var(--scale-factor)); }
  .w-responsive-12 { width: calc(3rem * var(--scale-factor)); }
  .w-responsive-16 { width: calc(4rem * var(--scale-factor)); }
  .w-responsive-20 { width: calc(5rem * var(--scale-factor)); }
  .w-responsive-24 { width: calc(6rem * var(--scale-factor)); }

  .h-responsive-4 { height: calc(1rem * var(--scale-factor)); }
  .h-responsive-6 { height: calc(1.5rem * var(--scale-factor)); }
  .h-responsive-8 { height: calc(2rem * var(--scale-factor)); }
  .h-responsive-10 { height: calc(2.5rem * var(--scale-factor)); }
  .h-responsive-12 { height: calc(3rem * var(--scale-factor)); }
  .h-responsive-16 { height: calc(4rem * var(--scale-factor)); }
  .h-responsive-20 { height: calc(5rem * var(--scale-factor)); }
  .h-responsive-24 { height: calc(6rem * var(--scale-factor)); }
}

