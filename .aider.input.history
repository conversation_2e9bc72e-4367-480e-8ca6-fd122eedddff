
# 2025-06-20 23:12:15.490433
+y

# 2025-06-20 23:12:34.861433
+n

# 2025-06-20 23:14:39.114799
+navigate 跳转时，如果我当前的页面处在中间位置，跳转到另一个页面时，浏览器没有自动定位到顶部

# 2025-06-20 23:14:52.179085
+y

# 2025-06-20 23:15:36.906592
+ 

# 2025-06-21 11:24:09.162923
+n

# 2025-06-21 11:24:16.933159
+/g

# 2025-06-21 11:24:27.288560
+clear

# 2025-06-21 11:24:49.130410
+/commit

# 2025-06-21 11:37:20.594199
+/code

# 2025-06-21 11:38:04.838610
+/add

# 2025-06-21 11:39:06.935070
+把首页整体的字体、边距调到现在的0.75

# 2025-06-21 11:39:20.685478
+y

# 2025-06-21 11:39:29.403395
+A

# 2025-06-21 16:33:27.079420
+/commit

# 2025-06-21 16:34:03.191965
+clear

# 2025-06-21 16:34:55.354705
+n

# 2025-06-21 16:35:26.155787
+DetailLine中的折线图，每个数据点位的圈调大点

# 2025-06-21 16:35:34.798104
+y

# 2025-06-21 16:41:07.007376
+/commit

# 2025-06-21 16:41:13.815782
+/cl

# 2025-06-21 16:41:19.499796
+/clear

# 2025-06-21 16:43:05.562029
+参考home-table，修改stock-dialog中表格的滚动，让滚动条只在tbody中

# 2025-06-21 16:43:37.148086
+src/features/home/<USER>/stock-dialog.tsx

# 2025-06-21 16:43:40.904321
+y

# 2025-06-21 17:00:15.506688
+/commit
